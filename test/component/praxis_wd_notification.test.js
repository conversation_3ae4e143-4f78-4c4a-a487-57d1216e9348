'use strict'

const expect = require('chai').expect
const rp = require('request-promise')
const httpStatusCodes = require('http-status-codes')
const uuid = require('uuid')
const mockServerClient = require('./utils/mock_server_client')
const config = require('../../lib/config')
const topology = require('./utils/test_rabbitmq_topology')
const generatePraxisSignature = require('./utils/generatePraxisSignature')

describe('Praxis notification call', () => {
  const customerId = '265847'
  const customerCid = '2003578'
  const paymentAccountId = Date.now().toString()
  const tradingAccountId = uuid.v4()

  const wdNotifBody = tid => ({
    merchant_id: 'User-Tradingmoon',
    application_key: 'Tradingmoon TEST',
    customer: {
      customer_token: '1234aaa'
    },
    session: {
      auth_token: '3388f0015845d534b0f853c4067e215a',
      intent: 'withdrawal',
      order_id: '3388f0015845d534b0f853c4067e215a',
      payment_method: 'Manual_BankWire',
      cid: customerCid,
      variable1: `a69cb580-c887-4ab5-a45c-a15895e921eb,1761-session-id,${customerId}`,
      variable2: null,
      variable3: null
    },
    transaction: {
      transaction_status: 'requested',
      transaction_type: 'payout',
      tid: tid,
      transaction_id: '7349717',
      currency: 'USD',
      amount: 10200,
      processed_currency: 'USD',
      processed_amount: 10200,
      created_by: 'INTERNET',
      edited_by: null,
      is_cascade: 0,
      cascade_level: null,
      payment_method: 'altbankwire',
      payment_processor: 'Manual_BankWire',
      withdrawal_request_id: null,
      card: {
        card_exp: null,
        card_number: null,
        card_type: null
      }
    },
    version: '1.3',
    timestamp: **********
  })

  const wdApprovalBody = tid => ({
    merchant_id: 'User-Tradingmoon',
    application_key: 'Tradingmoon TEST',
    customer: {
      customer_token: '1234aaa'
    },
    session: {
      auth_token: 'c53486ec9d21fb64613bee3bab09e3f4',
      intent: 'withdrawal',
      order_id: '3388f0015845d534b0f853c4067e215a',
      payment_method: 'Manual_BankWire',
      cid: customerCid,
      variable1: `a69cb580-c887-4ab5-a45c-a15895e921eb,1761-session-id,${customerId}`,
      variable2: null,
      variable3: null
    },
    transaction: {
      transaction_status: 'some-status-that-wont-be-used',
      transaction_type: 'payout',
      tid: tid,
      transaction_id: '7370937',
      currency: 'USD',
      amount: 10200,
      processed_currency: 'USD',
      processed_amount: 10200,
      created_by: 'INTERNET',
      edited_by: null,
      is_cascade: 0,
      cascade_level: null,
      payment_method: 'altbankwire',
      payment_processor: 'Manual_BankWire',
      withdrawal_request_id: tid,  // note this reference will be used for db-lookup
      card: {
        card_exp: null,
        card_number: null,
        card_type: null
      }
    },
    version: '1.3',
    timestamp: **********
  })

  before(() => {
    return rp({
      url: `http://payment-service:${config.PORT}/payment/customer/${customerId}/mapping`,
      method: 'POST',
      body: {
        externalId: customerCid,
        paymentAccountId,
        tradingAccountId
      },
      json: true
    })
  })

  beforeEach(() => {
    // Reset mock server to ensure clean state for each test
    return mockServerClient.reset()
      .then(() => {
        return Promise.all([
          mockServerClient.mockAnyResponse({
            httpRequest: {
              method: 'GET',
              path: `/v2/customers/${customerId}`
            },
            httpResponse: {
              statusCode: httpStatusCodes.OK,
              body: JSON.stringify({
                contactInformation: {
                  country: 'ES'
                },
                personalInformation: {}
              })
            },
            times: {
              unlimited: true
            }
          }),
          mockServerClient.mockAnyResponse({
            httpRequest: {
              method: 'GET',
              path: `/latest`,  // exchange-rate-service
              queryStringParameters: {
                base: ['USD']
              }
            },
            httpResponse: {
              statusCode: httpStatusCodes.OK,
              body: JSON.stringify({
                rates: {
                  EUR: 0.842045,
                  USD: 1
                }
              })
            },
            times: {
              remainingTimes: 1,
              unlimited: false
            }
          })
        ])
      })
  })

  const stubFindTxPraxisCall = (tid, txStatus = 'approved') => {
    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'POST',
        path: '/agent/find-transaction'  // praxis
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        headers: {
          'Content-Type': ['application/json; charset=utf-8'],
          [config.PRAXIS.AUTH_HDR]: ['23ab3ab33a436a1928e262f70671f684d5d2fa659842f6b744f3d400b7ee5338ee7c7d311550fcfbc5a4e031794ef7ef']
        },
        body: JSON.stringify({
          merchant_id: 'User-Tradingmoon',
          application_key: 'Tradingmoon TEST',
          customer: {
            customer_token: '1234aaa'
          },
          transaction: {
            transaction_status: txStatus,
            transaction_type: 'payout',
            tid: tid,
            processed_currency: 'USD',
            processed_amount: 10200
          },
          status: 0,
          version: '1.3',
          timestamp: **********
        })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })
  }

  it('should handle WD request and WD approval when user has enough withdrawable balance', function (done) {
    let transactionId
    const tid = **********
    const prefixedTid = `p${tid}`
    const from = Date.now()

    stubFindTxPraxisCall(tid)

    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'GET',
        path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        body: JSON.stringify({ withdrawableAmount: 102.00 })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })

    this.rabbitChannel.consume(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, message => {
      const content = JSON.parse(message.content.toString())
      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('REQUESTED')
      expect(content.data.reference).to.equal(prefixedTid)
    }, { noAck: true, consumerTag: 'praxis-requested' })

    this.rabbitChannel.consume(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, message => {
      const content = JSON.parse(message.content.toString())

      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('RELEASED')
      expect(content.data.reference).to.equal(prefixedTid)

      Promise.all([
        this.rabbitChannel.cancel('praxis-requested'),
        this.rabbitChannel.cancel('praxis-released')
      ])
        .then(() => done())
    }, { noAck: true, consumerTag: 'praxis-released' })

    rp({
      url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
      method: 'POST',
      body: wdNotifBody(tid),
      headers: {
        [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdNotifBody(tid)),
        'Content-Type': 'application/json; charset=utf-8'
      },
      json: true
    })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const to = Date.now()

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
          method: 'GET',
          json: true
        })
      })
      .then(transactionListResponse => {
        const transaction = transactionListResponse.rows[0]

        transactionId = transaction.id

        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.currencySymbol).to.equal('$')
        expect(transaction.cardLastFourDigits).to.equal(null)
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REQUESTED')
        expect(transaction.reference).to.equal(prefixedTid)

        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: wdApprovalBody(tid),
          headers: {
            [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdApprovalBody(tid)),
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdApprovalResponse => {
        expect(wdApprovalResponse.status).to.equal(0)
        expect(wdApprovalResponse.description).to.equal('Ok')

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('RELEASED')
        expect(transaction.reference).to.equal(prefixedTid)
      })
      .catch(done)
  })

  it('should cancel WD request if user does not have enough withdrawable balance', function (done) {
    const tid = **********
    const from = Date.now()

    stubFindTxPraxisCall(tid)

    // Clear all previous mock expectations and reset the mock server
    mockServerClient.reset()
      .then(() => {
        this.rabbitChannel.purgeQueue(topology.ALL_TEST_QUEUE.name)

        // Mock the balance service to return insufficient balance
        return mockServerClient.mockAnyResponse({
          httpRequest: {
            method: 'GET',
            path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
          },
          httpResponse: {
            statusCode: httpStatusCodes.OK,
            body: JSON.stringify({ withdrawableAmount: 101.99 })  // !! note withdrawable balance is not enough
          },
          times: {
            remainingTimes: 1,
            unlimited: false
          }
        })
      })
      .then(() => {
        // Mock the Praxis cancellation endpoint
        return mockServerClient.mockAnyResponse({
          httpRequest: {
            method: 'POST',
            path: '/agent/manage-withdrawal-request'  // praxis
          },
          httpResponse: {
            statusCode: httpStatusCodes.OK,
            headers: {
              'Content-Type': ['application/json; charset=utf-8'],
              [config.PRAXIS.AUTH_HDR]: ['23ab3ab33a436a1928e262f70671f684d5d2fa659842f6b744f3d400b7ee5338ee7c7d311550fcfbc5a4e031794ef7ef']
            },
            body: JSON.stringify({
              merchant_id: 'User-Tradingmoon',
              application_key: 'Tradingmoon TEST',
              customer: {
                customer_token: '1234aaa'
              },
              transaction: {
                transaction_status: 'approved',
                transaction_type: 'payout',
                tid: tid,
                processed_currency: 'USD',
                processed_amount: 10200
              },
              status: 0,
              version: '1.3',
              timestamp: **********
            })
          },
          times: {
            remainingTimes: 1,
            unlimited: false
          }
        })
      })
        // Set up consumer to catch any unexpected messages
        this.rabbitChannel.consume(topology.ALL_TEST_QUEUE.name, message => {
          throw new Error(`unexpected message received: ${JSON.stringify(message)}`)
        }, { noAck: true, consumerTag: 'praxis-all' })

        // Send the withdrawal notification
        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: wdNotifBody(tid),
          headers: {
            [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdNotifBody(tid)),
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        // Wait for the cancellation delay plus some buffer time
        return new Promise(resolve => {
          setTimeout(() => {
            const to = Date.now()
            resolve(rp({
              url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
              method: 'GET',
              json: true
            }))
          }, config.PRAXIS.CANCEL_WD_REQUEST_DELAY_MS + 1000) // Add buffer time
        })
      })
      .then(transactionListResponse => {
        expect(transactionListResponse.count).to.equal(0)  // ie tx request didn't get written to db

        // Verify that exactly one cancellation request was made
        return mockServerClient.verify({
          path: '/agent/manage-withdrawal-request',
          method: 'POST',
          body: {
            type: 'JSON_PATH',
            jsonPath: '$.[?(@.merchant_id == "User-Tradingmoon")]'
          }
        }, 1, 1)
      })
      .then(() => {
        // Clean up and finish test
        return this.rabbitChannel.cancel('praxis-all')
      })
      .then(() => done())
      .catch(done)
  })

  it('should release funds if  WD request is cancelled', function (done) {
    let transactionId
    const tid = **********
    const prefixedTid = `p${tid}`
    const from = Date.now()

    stubFindTxPraxisCall(tid)

    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'GET',
        path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        body: JSON.stringify({ withdrawableAmount: 105.20 })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })

    this.rabbitChannel.consume(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, message => {
      const content = JSON.parse(message.content.toString())
      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('REQUESTED')
      expect(content.data.reference).to.equal(prefixedTid)
    }, { noAck: true, consumerTag: 'praxis-requested' })

    this.rabbitChannel.consume(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, message => {
      throw new Error(`unexpected message received: ${JSON.stringify(message)}`)
    }, { noAck: true, consumerTag: 'praxis-released' })

    this.rabbitChannel.consume(topology.REJECTED_WITHDRAWAL_TEST_QUEUE.name, message => {
      const content = JSON.parse(message.content.toString())

      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('REJECTED')
      expect(content.data.reference).to.equal(prefixedTid)

      Promise.all([
        this.rabbitChannel.cancel('praxis-requested'),
        this.rabbitChannel.cancel('praxis-rejected'),
        this.rabbitChannel.cancel('praxis-released')
      ])
        .then(() => done())
    }, { noAck: true, consumerTag: 'praxis-rejected' })

    rp({
      url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
      method: 'POST',
      body: wdNotifBody(tid),
      headers: {
        [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdNotifBody(tid)),
        'Content-Type': 'application/json; charset=utf-8'
      },
      json: true
    })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const to = Date.now()

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
          method: 'GET',
          json: true
        })
      })
      .then(transactionListResponse => {
        const transaction = transactionListResponse.rows[0]

        transactionId = transaction.id

        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.currencySymbol).to.equal('$')
        expect(transaction.cardLastFourDigits).to.equal(null)
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REQUESTED')
        expect(transaction.reference).to.equal(prefixedTid)

        const cancelledStatusNotif = wdNotifBody(tid)
        cancelledStatusNotif.transaction.transaction_status = 'cancelled'

        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: cancelledStatusNotif,
          headers: {
            [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(cancelledStatusNotif),
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdApprovalResponse => {
        expect(wdApprovalResponse.status).to.equal(0)
        expect(wdApprovalResponse.description).to.equal('Ok')

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REJECTED')
        expect(transaction.reference).to.equal(prefixedTid)
      })
      .catch(done)
  })

  it('should ignore 2nd status=requested /notification message', function (done) {
    let transactionId
    const tid = **********
    const prefixedTid = `p${tid}`
    const from = Date.now()
    let wdRequestedMsgReceived = 0

    stubFindTxPraxisCall(tid)

    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'GET',
        path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        body: JSON.stringify({ withdrawableAmount: 102.00 })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })

    this.rabbitChannel.consume(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, message => {
      wdRequestedMsgReceived++

      const content = JSON.parse(message.content.toString())
      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('REQUESTED')
      expect(content.data.reference).to.equal(prefixedTid)
    }, { noAck: true, consumerTag: 'praxis-requested' })

    this.rabbitChannel.consume(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, message => {
      throw new Error(`unexpected status=RELEASED message received: ${JSON.stringify(message)}`)
    }, { noAck: true, consumerTag: 'praxis-released' })

    rp({
      url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
      method: 'POST',
      body: wdNotifBody(tid),
      headers: {
        [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdNotifBody(tid)),
        'Content-Type': 'application/json; charset=utf-8'
      },
      json: true
    })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const to = Date.now()

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
          method: 'GET',
          json: true
        })
      })
      .then(transactionListResponse => {
        const transaction = transactionListResponse.rows[0]

        transactionId = transaction.id

        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.currencySymbol).to.equal('$')
        expect(transaction.cardLastFourDigits).to.equal(null)
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REQUESTED')
        expect(transaction.reference).to.equal(prefixedTid)

        // re-send the same status=requested /notification callback:
        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: wdNotifBody(tid),
          headers: {
            [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(wdNotifBody(tid)),
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdApprovalResponse => {
        expect(wdApprovalResponse.status).to.equal(0)
        expect(wdApprovalResponse.description).to.equal('Ok')

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REQUESTED')  // status still REQUESTED
        expect(transaction.reference).to.equal(prefixedTid)

        setTimeout(() => {  // allow for some time for potential erroneously sent messages to be received
          if (wdRequestedMsgReceived === 1) {
            Promise.all([
              this.rabbitChannel.cancel('praxis-requested'),
              this.rabbitChannel.cancel('praxis-released')
            ]).then(() => done())
          } else {
            throw new Error(`${wdRequestedMsgReceived} REQUESTED domain messages received, expected 1`)
          }
        }, 1000)
      })
      .catch(done)
  })

  it('should NOT send WithdrawRequestRejected event if we get negative status /notification when not having received status=requested first', function (done) {
    let transactionId
    const tid = **********
    const prefixedTid = `p${tid}`
    const from = Date.now()

    this.rabbitChannel.purgeQueue(topology.ALL_TEST_QUEUE.name)

    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'GET',
        path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        body: JSON.stringify({ withdrawableAmount: 102.00 })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })

    this.rabbitChannel.consume(topology.ALL_TEST_QUEUE.name, message => {
      throw new Error(`unexpected message received: ${JSON.stringify(message)}`)
    }, { noAck: true, consumerTag: 'praxis-all' })

    const initializedStatusNotif = wdNotifBody(tid)
    initializedStatusNotif.transaction.transaction_status = 'initialized'

    rp({
      url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
      method: 'POST',
      body: initializedStatusNotif,
      headers: {
        [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(initializedStatusNotif),
        'Content-Type': 'application/json; charset=utf-8'
      },
      json: true
    })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const cancelledStatusNotif = wdNotifBody(tid)
        cancelledStatusNotif.transaction.transaction_status = 'cancelled'

        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: cancelledStatusNotif,
          headers: {
            [config.PRAXIS.AUTH_HDR]: generatePraxisSignature(cancelledStatusNotif),
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const to = Date.now()

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
          method: 'GET',
          json: true
        })
      })
      .then(transactionListResponse => {
        const transaction = transactionListResponse.rows[0]

        transactionId = transaction.id

        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.currencySymbol).to.equal('$')
        expect(transaction.cardLastFourDigits).to.equal(null)
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REJECTED')
        expect(transaction.reference).to.equal(prefixedTid)

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REJECTED')
        expect(transaction.reference).to.equal(prefixedTid)

        setTimeout(() => {  // allow for some time for potential erroneously sent messages to be received
          this.rabbitChannel.cancel('praxis-all').then(() => done())
        }, 1000)
      })
      .catch(done)
  })
})
